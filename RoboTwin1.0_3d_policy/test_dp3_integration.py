#!/usr/bin/env python3
"""
DP3策略集成测试脚本
用于验证训练好的DP3策略与RoboTwin评估环境的集成
"""

import numpy as np
import os
import cv2
from PIL import Image
from datetime import datetime
from script.eval_3dpolicy import Env
import sys

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)
# 获取脚本所在的目录，也就是 RoboTwin1.0_3d_policy/ 目录
project_root_dir = os.path.dirname(current_script_path)
# 将项目根目录添加到sys.path，以便正确导入项目内部模块
sys.path.append(project_root_dir)

def create_image_dir(task_name, seed):
    """创建图像保存目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    image_base_dir = os.path.join(project_root_dir, "observation_images")
    image_dir = os.path.join(image_base_dir, f"{task_name}_{seed}_{timestamp}")
    os.makedirs(image_dir, exist_ok=True)
    return image_dir

def save_image(image_array, save_dir, step, camera_name="front_camera"):
    """保存numpy格式图像到文件"""
    # 确保正确的图像格式 (RGB格式numpy数组)
    if image_array.dtype != np.uint8:
        # 如果是float类型且范围是[0,1]，转换为[0,255]
        if image_array.max() <= 1.0:
            image_array = (image_array * 255).astype(np.uint8)
        else:
            image_array = image_array.astype(np.uint8)
    
    # 使用OpenCV保存 (需要将RGB转换为BGR)
    image_bgr = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
    image_path = os.path.join(save_dir, f"{camera_name}_step_{step:04d}.png")
    cv2.imwrite(image_path, image_bgr)
    return image_path

class SimpleDP3PolicyLoader:
    """简化的DP3策略加载器"""
    
    def __init__(self):
        self.dp3_policy = None
        self.load_success = False
        
        # 尝试加载DP3策略
        self.load_dp3_policy()
    
    def load_dp3_policy(self):
        """加载DP3策略"""
        try:
            # 添加DP3策略路径
            dp3_policy_path = os.path.join(project_root_dir, 'policy/3D-Diffusion-Policy/3D-Diffusion-Policy')
            if dp3_policy_path not in sys.path:
                sys.path.insert(0, dp3_policy_path)
            
            # 导入DP3类
            from dp3_policy import DP3
            
            # 使用现有的评估脚本中的方法
            import hydra
            
            # 设置配置路径 - 使用主3D-Diffusion-Policy的配置
            main_config_path = os.path.join(os.path.dirname(project_root_dir), '3D-Diffusion-Policy/diffusion_policy_3d/config')
            
            # 创建一个基本配置
            with hydra.initialize(config_path=main_config_path, version_base=None):
                cfg = hydra.compose(config_name="dp3.yaml", overrides=["task=block_hammer_beat"])
                
                # 修改配置以匹配我们的任务
                cfg.training.seed = 0
                
                # 创建DP3实例，使用checkpoint 2800
                self.dp3_policy = DP3(cfg, checkpoint_num=2800)
                self.load_success = True
                print(f"✓ 成功加载DP3策略，checkpoint: 2800")
                
        except Exception as e:
            print(f"✗ 加载DP3策略失败: {e}")
            import traceback
            traceback.print_exc()
            self.load_success = False
    
    def reset_obs_history(self):
        """重置观测历史"""
        if self.dp3_policy and hasattr(self.dp3_policy, 'env_runner'):
            self.dp3_policy.env_runner.reset_obs()
    
    def predict_action(self, observation):
        """预测动作"""
        if not self.load_success or self.dp3_policy is None:
            raise ValueError("DP3策略未成功加载")
        
        # 转换观测格式为DP3期望的格式
        obs_dict = {
            'point_cloud': observation['pointcloud'],
            'agent_pos': observation['joint_action']
        }
        
        # 使用DP3策略预测动作
        action = self.dp3_policy.get_action(obs_dict)
        
        return action

def main():
    """主测试函数"""
    print("=" * 60)
    print("DP3策略集成测试")
    print("=" * 60)
    
    # 1. 创建环境管理器实例
    print("1. 初始化环境管理器...")
    env_manager = Env()
    
    # 2. 创建特定任务环境并验证seeds
    task_name = "block_hammer_beat"
    seed = 42
    task_num = 1  # 只测试一个任务
    
    print(f"2. 创建任务环境: {task_name}")
    seed_list, id_list = env_manager.Create_env(
        task_name=task_name,
        head_camera_type="D435", 
        seed=seed,
        task_num=task_num
    )
    
    print(f"   找到 {len(seed_list)} 个有效任务seeds: {seed_list}")
    
    # 3. 初始化DP3策略加载器
    print("3. 初始化DP3策略加载器...")
    try:
        dp3_policy = SimpleDP3PolicyLoader()
        if dp3_policy.load_success:
            print("   ✓ DP3策略加载成功！")
        else:
            print("   ✗ DP3策略加载失败，将使用随机动作")
            dp3_policy = None
    except Exception as e:
        print(f"   ✗ DP3策略加载失败: {e}")
        dp3_policy = None
    
    # 4. 运行任务测试
    print("4. 开始任务测试...")
    for i, (seed, task_id) in enumerate(zip(seed_list, id_list)):
        print(f"\n   执行任务 {i+1}/{len(seed_list)}, seed: {seed}")
        
        # 创建图像保存目录
        image_dir = create_image_dir(task_name, seed)
        print(f"   图像将保存到: {image_dir}")
        
        # 初始化任务环境
        env_manager.Init_task_env(seed, task_id)
        
        # 重置DP3策略的观测历史（如果策略加载成功）
        if dp3_policy is not None and dp3_policy.load_success:
            dp3_policy.reset_obs_history()
        
        # 运行任务循环
        max_steps = 5  # 只测试5步
        for step in range(max_steps):
            print(f"     Step {step+1}/{max_steps}")
            
            # 获取观测
            observation = env_manager.get_observation()
            
            # 保存图像（可选）
            try:
                if 'front_camera' in observation['observation'] and 'rgb' in observation['observation']['front_camera']:
                    front_image = observation['observation']['front_camera']['rgb']
                    save_image(front_image, image_dir, step, "front_camera")
            except Exception as e:
                print(f"       警告: 保存图像失败 - {e}")
            
            # 计算动作
            if dp3_policy is not None and dp3_policy.load_success:
                try:
                    # 使用DP3策略预测动作
                    action = dp3_policy.predict_action(observation)
                    print(f"       ✓ 使用DP3策略预测动作，形状: {action.shape}")
                except Exception as e:
                    print(f"       ✗ DP3策略预测失败: {e}，使用随机动作")
                    action = np.random.uniform(-1, 1, (1, 16))
                    action = np.array(action)
            else:
                # 使用随机动作作为备选方案
                action = np.random.uniform(-1, 1, (1, 16))
                action = np.array(action)
                print(f"       使用随机动作，形状: {action.shape}")
            
            # 执行动作并获取状态
            status = env_manager.Take_action(action)
            print(f"       动作执行状态: {status}")
            
            # 如果任务完成或失败则退出循环
            if status != "run":
                print(f"       任务结束: {status}")
                break
        
        # 确保环境关闭
        if status == "run":
            env_manager.Close_env()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
