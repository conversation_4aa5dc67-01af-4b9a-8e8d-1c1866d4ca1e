import numpy as np
import os
import cv2
from PIL import Image
from datetime import datetime
from script.eval_3dpolicy import Env
import sys
import torch
import pathlib
import dill
from collections import deque

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)
print(current_script_path)
# 获取脚本所在的目录，也就是 RoboTwin1.0_3d_policy/ 目录
project_root_dir = os.path.dirname(current_script_path)
print(project_root_dir) # /data/sea_disk0/wushr/3D-Policy/DP3Encoder-to-Uni3D/3D-Diffusion-Policy/RoboTwin1.0_3d_policy
# 将项目根目录添加到sys.path，以便正确导入项目内部模块
sys.path.append(project_root_dir)

# 添加3D-Diffusion-Policy路径以导入DP3相关模块
dp3_path = os.path.join(os.path.dirname(project_root_dir), '3D-Diffusion-Policy')
print(dp3_path) # /data/sea_disk0/wushr/3D-Policy/DP3Encoder-to-Uni3D/3D-Diffusion-Policy/3D-Diffusion-Policy
sys.path.insert(0, dp3_path)

# 添加openpoints模块路径
openpoints_path = os.path.join(os.path.dirname(project_root_dir), 'openpoints')
if openpoints_path not in sys.path:
    sys.path.insert(0, openpoints_path)
    print(f"Added openpoints path: {openpoints_path}")

from diffusion_policy_3d.common.pytorch_util import dict_apply

def load_dp3_policy():
    """加载DP3策略的简化函数"""
    try:

        # 添加DP3策略路径
        dp3_policy_path = dp3_path # os.path.join(dp3_path, 'diffusion_policy_3d/policy/3D-Diffusion-Policy/3D-Diffusion-Policy')
        if dp3_policy_path not in sys.path:
            sys.path.insert(0, dp3_policy_path)

        # 导入DP3类
        from eval import DP3_policy

        # 使用现有的评估脚本中的方法
        import hydra

        # 设置配置路径 - 使用RoboTwin中的DP3配置
        config_path = "../3D-Diffusion-Policy/diffusion_policy_3d/config"   # os.path.join(dp3_policy_path, 'diffusion_policy_3d', 'config')

        # 创建一个基本配置
        with hydra.initialize(config_path=config_path, version_base=None):
            cfg = hydra.compose(config_name="dp3.yaml", overrides=["task=block_hammer_beat"])

            # # 修改配置以匹配我们的任务
            # # cfg.training.seed = 0
            # cfg.checkpoint_num = 2800
            # cfg.expert_data_num = 0
            # cfg.head_camera_type = "D435"

            # 创建DP3实例，使用checkpoint 2800
            dp3_policy = DP3_policy(cfg, checkpoint_num=2800).get_policy()
            print(f"✓ 成功加载DP3策略，checkpoint: 2800")
            return dp3_policy

    except Exception as e:
        print(f"✗ 加载DP3策略失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    
# def load_RoboTwin_runner():
#     """加载RoboTwin的runner"""
#     try:
#         # 添加路径
#         runner_path = os.path.join(project_root_dir, 'policy/3D-Diffusion-Policy/3D-Diffusion-Policy')
#         if runner_path not in sys.path:
#             sys.path.insert(0, runner_path)

#         # 导入runner
#         from dp3_policy import 

#         # 使用现有的评估脚本中的方法
#         import hydra

#         # 设置配置路径 - 使用RoboTwin中的DP3配置
#         config_path = os.path.join(dp3_policy_path, 'diffusion_policy_3d', 'config')

#         # 创建一个基本配置
#         with hydra.initialize(config_path=config_path, version_base=None):
#             cfg = hydra.compose(config_name="dp3.yaml", overrides=["task=block_hammer_beat"])

#             # # 修改配置以匹配我们的任务
#             # # cfg.training.seed = 0
#             # cfg.checkpoint_num = 2800
#             # cfg.expert_data_num = 0
#             # cfg.head_camera_type = "D435"

#             # 创建DP3实例，使用checkpoint 2800
#             dp3_policy = DP3_policy(cfg, checkpoint_num=2800).get_policy()
#             print(f"✓ 成功加载DP3策略，checkpoint: 2800")
#             return dp3_policy

#     except Exception as e:
#         print(f"✗ 加载DP3策略失败: {e}")
#         import traceback
#         traceback.print_exc()
#         return None

# Create image save directory
def create_image_dir(task_name, seed):
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    image_base_dir = os.path.join(project_root_dir, "observation_images")
    image_dir = os.path.join(image_base_dir, f"{task_name}_{seed}_{timestamp}")
    os.makedirs(image_dir, exist_ok=True)
    return image_dir

# Save image function
def save_image(image_array, save_dir, step, camera_name="front_camera"):
    """Save numpy format image to file"""
    # Ensure correct image format (RGB format numpy array)
    if image_array.dtype != np.uint8:
        # If float type and range is [0,1], convert to [0,255]
        if image_array.max() <= 1.0:
            image_array = (image_array * 255).astype(np.uint8)
        else:
            image_array = image_array.astype(np.uint8)
    
    # Save using OpenCV (need to convert RGB to BGR)
    image_bgr = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
    image_path = os.path.join(save_dir, f"{camera_name}_step_{step:04d}.png")
    cv2.imwrite(image_path, image_bgr)
    return image_path

# 1. Create environment manager instance
env_manager = Env()

# 2. Create specific task environment and validate seeds
task_name = "block_hammer_beat"
seed = 42
task_num = 5

# 3. 初始化DP3策略
# 使用训练好的block_hammer_beat checkpoint (checkpoint编号2800)
print(f"正在加载DP3策略，checkpoint: 2800")
dp3_policy = load_dp3_policy()
if dp3_policy is None:
    print("将使用随机动作作为备选方案")

seed_list, id_list = env_manager.Create_env(
    task_name=task_name,
    head_camera_type="D435", 
    seed=seed,
    task_num=task_num
)

print(f"Found {len(seed_list)} valid task seeds: {seed_list}")

# 3. Run tasks for each valid seed
for i, (seed, task_id) in enumerate(zip(seed_list, id_list)):
    print(f"\nExecuting task {i+1}/{len(seed_list)}, seed: {seed}")
    
    # Create image save directory for current task
    image_dir = create_image_dir(task_name, seed)
    print(f"Images will be saved to: {image_dir}")
    
    # Initialize task environment
    env_manager.Init_task_env(seed, task_id)

    # 重置DP3策略的观测历史（如果策略加载成功）
    # if dp3_policy is not None and hasattr(dp3_policy, 'env_runner'):
    #     dp3_policy.env_runner.reset_obs()

    # Run task loop
    max_steps = 1000
    obs_history = deque(maxlen=2)  # 保存最近2个时间步的观测
    for step in range(max_steps):
        # Get observation
        observation = env_manager.get_observation()
        
        # Extract and save images from different cameras
        try:
            # Front camera image
            if 'front_camera' in observation['observation'] and 'rgb' in observation['observation']['front_camera']:
                front_image = observation['observation']['front_camera']['rgb']
                front_path = save_image(front_image, image_dir, step, "front_camera")
                print(f"Saved front camera image: {front_path}")
            
            # Head camera image (if exists)
            if 'head_camera' in observation['observation'] and 'rgb' in observation['observation']['head_camera']:
                head_image = observation['observation']['head_camera']['rgb']
                head_path = save_image(head_image, image_dir, step, "head_camera")
                print(f"Saved head camera image: {head_path}")
            
            # Wrist camera image (if exists)
            if 'wrist_camera' in observation['observation'] and 'rgb' in observation['observation']['wrist_camera']:
                wrist_image = observation['observation']['wrist_camera']['rgb']
                wrist_path = save_image(wrist_image, image_dir, step, "wrist_camera")
                print(f"Saved wrist camera image: {wrist_path}")
            
        except KeyError as e:
            print(f"Warning: Unable to access image data - {e}")
            # Print observation structure for debugging
            print(f"Observation data structure: {list(observation.keys())}")
            if 'observation' in observation:
                print(f"Camera data structure: {list(observation['observation'].keys())}")
        
        # Calculate action based on observation
        if dp3_policy is not None:


            try:
                # 将当前观测添加到历史中
                current_obs = {
                    'point_cloud': torch.from_numpy(observation['pointcloud']),
                    'agent_pos': torch.from_numpy(observation['joint_action'])
                }
                obs_history.append(current_obs)
                
                # 如果历史不足2步，用当前观测填充
                if len(obs_history) < 2:
                    # 复制当前观测来填充历史
                    while len(obs_history) < 2:
                        obs_history.appendleft(current_obs.copy())
                
                # 构建时间序列观测数据 (batch=1, time=2, ...)
                obs_dict = {
                    'point_cloud': torch.stack([obs['point_cloud'] for obs in obs_history], dim=0).unsqueeze(0),
                    'agent_pos': torch.stack([obs['agent_pos'] for obs in obs_history], dim=0).unsqueeze(0)
                }

                # 打印转换后的数据形状
                print(f"转换后 point_cloud shape: {obs_dict['point_cloud'].shape}")  # 应该是 (1, 2, 1024, 6)
                print(f"转换后 agent_pos shape: {obs_dict['agent_pos'].shape}") 

                action_dict = dp3_policy.predict_action(obs_dict)
                action = action_dict['action'].squeeze(0).detach().cpu().numpy()
                print(f"Step {step}: 使用DP3策略预测动作，形状: {action.shape}")
                # 输出每一步的动作
                for i in range(action.shape[0]):
                    print(f"第{i+1}步动作: {action[i]}")

            # try:
            #     # 转换观测格式为DP3期望的格式
            #     obs_dict = {
            #         'point_cloud': torch.from_numpy(observation['pointcloud']).unsqueeze(0).unsqueeze(0),
            #         'agent_pos': torch.from_numpy(observation['joint_action']).unsqueeze(0).unsqueeze(0)
            #     }

            #     # 打印转换后的数据形状
            #     print(f"转换后 point_cloud shape: {obs_dict['point_cloud'].shape}")
            #     print(f"转换后 agent_pos shape: {obs_dict['agent_pos'].shape}")

                # 使用DP3策略预测动作
                # action = dp3_policy.predict_action(obs_dict)
                # print(f"Step {step}: 使用DP3策略预测动作，形状: {action.shape}")
            except Exception as e:
                print(f"Step {step}: DP3策略预测失败: {e}，使用随机动作")
                action = np.random.uniform(-1, 1, (1, 16))
                action = np.array(action)
        else:
            # 使用随机动作作为备选方案
            action = np.random.uniform(-1, 1, (1, 16))
            action = np.array(action)
            print(f"Step {step}: 使用随机动作，形状: {action.shape}")
        
        # Execute action and get status
        status = env_manager.Take_action(action)
        print(f"Step {step}: status = {status}")
        
        # Exit loop if task is completed or failed
        if status != "run":
            break
    
    # Ensure environment is closed
    if status == "run":
        env_manager.Close_env()

print("\nAll tasks completed, images have been saved.")