if __name__ == "__main__":
    import sys
    import os
    import pathlib

    ROOT_DIR = str(pathlib.Path(__file__).parent.parent.parent)
    sys.path.append(ROOT_DIR)
    os.chdir(ROOT_DIR)

import os
import hydra
import torch
import dill
from omegaconf import OmegaConf
import pathlib
from train import TrainDP3Workspace

OmegaConf.register_new_resolver("eval", eval, replace=True)
    

@hydra.main(
    version_base=None,
    config_path=str(pathlib.Path(__file__).parent.joinpath(
        'diffusion_policy_3d', 'config'))
)
def main(cfg):
    workspace = TrainDP3Workspace(cfg)
    workspace.eval()

# class DP3_policy:
#     def __init__(self, cfg, checkpoint_num) -> None:
#         self.cfg = cfg
#         self.checkpoint_num = checkpoint_num
#         # self.policy = self.get_policy(cfg, checkpoint_num)
        
#     def update_obs(self, observation):
#         self.env_runner.update_obs(observation)
    
#     # def get_action(self, observation):
#     #     action = self.env_runner.get_action(self.policy, observation)
#     #     return action    

#     # def get_action(self, observaton=None) -> bool: # by tianxing chen
#     #     if observaton == None:
#     #         print('==== Get empty observation ===')
#     #         return False
#     #     device, dtype = self.policy.device, self.policy.dtype
#     #     self.obs.append(observaton) # update
#     #     obs = self.get_n_steps_obs()

#     #     # create obs dict
#     #     np_obs_dict = dict(obs)
#     #     # device transfer
#     #     obs_dict = dict_apply(np_obs_dict, lambda x: torch.from_numpy(x).to(device=device))
#     #     # run policy
#     #     with torch.no_grad():
#     #         obs_dict_input = {}  # flush unused keys
#     #         obs_dict_input['point_cloud'] = obs_dict['point_cloud'].unsqueeze(0)
#     #         obs_dict_input['agent_pos'] = obs_dict['agent_pos'].unsqueeze(0)
            
#     #         action_dict = policy.predict_action(obs_dict_input)
            
#     #     # device_transfer
#     #     np_action_dict = dict_apply(action_dict, lambda x: x.detach().to('cpu').numpy())
#     #     action = np_action_dict['action'].squeeze(0)
#     #     return action

#     def get_policy(self):
#         workspace = TrainDP3Workspace(self.cfg, '/data/sea_disk0/wushr/3D-Policy/DP3Encoder-to-Uni3D/3D-Diffusion-Policy/3D-Diffusion-Policy/data/outputs/block_hammer_beat-dp3-9999_seed0')
#         policy = workspace.get_policy(self.cfg, self.checkpoint_num)
#         return policy

if __name__ == "__main__":
    main()
